<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Student Groups</h1>
                    <p class="text-gray-600 mt-1">Join groups to connect with students who share your interests</p>
                </div>
                <a href="<?php echo e(route('groups.create')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                    Create Group
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <form method="GET" class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-64 relative" id="group-search-container">
                    <label for="group-search-input" class="block text-sm font-medium text-gray-700 mb-1">Search Groups</label>
                    <input type="text"
                           id="group-search-input"
                           placeholder="Search by group name..."
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">

                    <!-- Search Results Dropdown -->
                    <div id="group-search-results"
                         class="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 max-h-96 overflow-y-auto z-50 hidden">
                        <div id="group-search-results-content">
                            <!-- Results will be populated here -->
                        </div>
                        <div id="group-search-loading" class="hidden p-4 text-center text-gray-500">
                            <svg class="animate-spin h-5 w-5 mx-auto" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="ml-2">Searching...</span>
                        </div>
                        <div id="group-search-no-results" class="hidden p-4 text-center text-gray-500">
                            <svg class="h-8 w-8 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <p class="text-sm">No groups found</p>
                        </div>
                    </div>
                </div>

                <div class="min-w-48">
                    <label for="organization_id" class="block text-sm font-medium text-gray-700 mb-1">Organization</label>
                    <select name="organization_id" id="organization_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Organizations</option>
                        <?php $__currentLoopData = $organizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $org): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($org->id); ?>" <?php echo e(request('organization_id') == $org->id ? 'selected' : ''); ?>>
                                <?php echo e($org->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="min-w-32">
                    <label for="visibility" class="block text-sm font-medium text-gray-700 mb-1">Visibility</label>
                    <select name="visibility" id="visibility" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Groups</option>
                        <option value="public" <?php echo e(request('visibility') === 'public' ? 'selected' : ''); ?>>Public</option>
                        <option value="private" <?php echo e(request('visibility') === 'private' ? 'selected' : ''); ?>>Private</option>
                    </select>
                </div>

                <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                    Filter
                </button>

                <?php if(request()->hasAny(['search', 'organization_id', 'visibility'])): ?>
                    <a href="<?php echo e(route('groups.index')); ?>" class="text-gray-500 hover:text-gray-700 px-3 py-2">
                        Clear
                    </a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Groups Grid -->
        <?php if($groups->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                        <!-- Cover Image -->
                        <div class="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                            <?php if($group->cover_image): ?>
                                <img src="<?php echo e(Storage::disk('public')->url($group->cover_image)); ?>" 
                                     alt="<?php echo e($group->name); ?>" 
                                     class="w-full h-full object-cover">
                            <?php endif; ?>
                            
                            <!-- Visibility Badge -->
                            <div class="absolute top-2 right-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($group->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                    <?php echo e(ucfirst($group->visibility)); ?>

                                </span>
                            </div>
                        </div>

                        <div class="p-4">
                            <!-- Group Logo and Info -->
                            <div class="flex items-start space-x-3 -mt-4 mb-3">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center border-2 border-white">
                                        <?php if($group->logo): ?>
                                            <img src="<?php echo e(Storage::disk('public')->url($group->logo)); ?>" 
                                                 alt="<?php echo e($group->name); ?>" 
                                                 class="w-10 h-10 rounded-lg object-cover">
                                        <?php else: ?>
                                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <span class="text-blue-600 font-bold text-sm"><?php echo e(substr($group->name, 0, 2)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="flex-1 min-w-0 pt-2">
                                    <h3 class="text-lg font-semibold text-gray-900 truncate">
                                        <a href="<?php echo e(route('groups.show', $group)); ?>" class="hover:text-blue-600">
                                            <?php echo e($group->name); ?>

                                        </a>
                                    </h3>
                                    <?php if($group->organization): ?>
                                        <p class="text-sm text-gray-500 truncate"><?php echo e($group->organization->name); ?></p>
                                    <?php endif; ?>
                                    <?php if($group->school || $group->campus): ?>
                                        <p class="text-xs text-gray-400 truncate">
                                            <?php if($group->school): ?>
                                                <?php echo e($group->school->abbreviation ?? $group->school->name); ?>

                                                <?php if($group->campus): ?>
                                                    - <?php echo e($group->campus->name); ?>

                                                <?php endif; ?>
                                            <?php elseif($group->campus): ?>
                                                <?php echo e($group->campus->name); ?>

                                            <?php endif; ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Description -->
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2"><?php echo e($group->description); ?></p>

                            <!-- Stats -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    <?php echo e($group->active_members_count); ?> members
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    <?php echo e($group->creator->name); ?>

                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="flex justify-end">
                                <a href="<?php echo e(route('groups.show', $group)); ?>" 
                                   class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                                    View Group
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                <?php echo e($groups->withQueryString()->links()); ?>

            </div>
        <?php else: ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No groups found</h3>
                <p class="mt-1 text-sm text-gray-500">
                    <?php if(request()->hasAny(['search', 'organization_id', 'visibility'])): ?>
                        Try adjusting your filters or search terms.
                    <?php else: ?>
                        Get started by creating the first group.
                    <?php endif; ?>
                </p>
                <?php if(!request()->hasAny(['search', 'organization_id', 'visibility'])): ?>
                    <div class="mt-6">
                        <a href="<?php echo e(route('groups.create')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700">
                            Create Group
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Group Search JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('group-search-input');
        const searchResults = document.getElementById('group-search-results');
        const searchContent = document.getElementById('group-search-results-content');
        const searchLoading = document.getElementById('group-search-loading');
        const searchNoResults = document.getElementById('group-search-no-results');
        let searchTimeout;

        if (searchInput) {
            // Handle search input
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();

                clearTimeout(searchTimeout);

                if (query.length === 0) {
                    hideSearchResults();
                    return;
                }

                // Show loading state
                showSearchResults();
                showLoading();

                // Dynamic search with minimal delay
                const delay = query.length === 1 ? 100 : 50;
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, delay);
            });

            // Handle focus and blur events
            searchInput.addEventListener('focus', function() {
                if (this.value.trim().length > 0) {
                    showSearchResults();
                }
            });

            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!document.getElementById('group-search-container').contains(e.target)) {
                    hideSearchResults();
                }
            });

            // Handle keyboard navigation
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    hideSearchResults();
                    this.blur();
                }
            });
        }

        function performSearch(query) {
            fetch(`/api/groups/search?q=${encodeURIComponent(query)}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success && data.groups.length > 0) {
                    displaySearchResults(data.groups);
                } else {
                    showNoResults();
                }
            })
            .catch(error => {
                console.error('Search error:', error);
                hideLoading();
                showNoResults();
            });
        }

        function displaySearchResults(groups) {
            searchContent.innerHTML = groups.map(group => {
                const logoHtml = group.logo_url
                    ? `<img src="${group.logo_url}" alt="${group.name}" class="w-10 h-10 rounded-lg object-cover">`
                    : `<div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                         <span class="text-blue-600 font-semibold text-sm">${group.name.substring(0, 2)}</span>
                       </div>`;

                const visibilityBadge = group.visibility === 'public'
                    ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Public</span>'
                    : '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Private</span>';

                return `
                    <div class="flex items-center p-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0">
                        <div class="flex-shrink-0 mr-3">
                            ${logoHtml}
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center gap-2 mb-1">
                                <div class="font-medium text-gray-900 truncate">${group.name}</div>
                                ${visibilityBadge}
                            </div>
                            <div class="text-sm text-gray-500 truncate">
                                ${group.active_members_count} members • Created by ${group.creator_name}
                                ${group.organization_name ? ` • ${group.organization_name}` : ''}
                            </div>
                            ${group.description ? `<div class="text-xs text-gray-400 truncate mt-1">${group.description}</div>` : ''}
                        </div>
                        <div class="flex-shrink-0 ml-3">
                            <a href="${group.url}" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                View
                            </a>
                        </div>
                    </div>
                `;
            }).join('');

            searchContent.classList.remove('hidden');
            searchNoResults.classList.add('hidden');
        }

        function showSearchResults() {
            searchResults.classList.remove('hidden');
        }

        function hideSearchResults() {
            searchResults.classList.add('hidden');
        }

        function showLoading() {
            searchLoading.classList.remove('hidden');
            searchContent.classList.add('hidden');
            searchNoResults.classList.add('hidden');
        }

        function hideLoading() {
            searchLoading.classList.add('hidden');
        }

        function showNoResults() {
            searchNoResults.classList.remove('hidden');
            searchContent.classList.add('hidden');
        }
    });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/groups/index.blade.php ENDPATH**/ ?>